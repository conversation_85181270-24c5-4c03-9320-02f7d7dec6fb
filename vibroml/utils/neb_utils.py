# vibroml/utils/neb_utils.py

import numpy as np
import os
import time
from ase.atoms import Atoms
from ase.io import write
from ase.optimize import BFGS
from ase.constraints import FixAtoms
from typing import List, Tuple, Optional, Dict, Any


def linear_interpolate_structures(initial_atoms: Atoms, final_atoms: Atoms, num_images: int) -> List[Atoms]:
    """
    Create intermediate images by linear interpolation between initial and final structures.
    
    Args:
        initial_atoms: Initial structure (ASE Atoms object)
        final_atoms: Final structure (ASE Atoms object)
        num_images: Number of intermediate images (excluding initial and final)
    
    Returns:
        List of ASE Atoms objects representing the complete path [initial, image1, ..., imageN, final]
    """
    print(f"Creating {num_images} intermediate images by linear interpolation...")
    
    # Validate structures have same number of atoms
    if len(initial_atoms) != len(final_atoms):
        raise ValueError(f"Initial and final structures must have the same number of atoms. "
                        f"Got {len(initial_atoms)} and {len(final_atoms)}")
    
    # Check if chemical symbols match
    if initial_atoms.get_chemical_symbols() != final_atoms.get_chemical_symbols():
        raise ValueError("Initial and final structures must have the same chemical composition")
    
    images = []
    
    # Add initial structure
    images.append(initial_atoms.copy())
    
    # Create intermediate images
    initial_positions = initial_atoms.get_positions()
    final_positions = final_atoms.get_positions()
    
    for i in range(1, num_images + 1):
        # Linear interpolation parameter
        alpha = i / (num_images + 1)
        
        # Interpolate positions
        interpolated_positions = (1 - alpha) * initial_positions + alpha * final_positions
        
        # Create new atoms object
        image = initial_atoms.copy()
        image.set_positions(interpolated_positions)
        
        # Interpolate cell parameters if they differ
        if not np.allclose(initial_atoms.cell, final_atoms.cell, atol=1e-6):
            initial_cell = initial_atoms.cell.array
            final_cell = final_atoms.cell.array
            interpolated_cell = (1 - alpha) * initial_cell + alpha * final_cell
            image.set_cell(interpolated_cell, scale_atoms=False)
        
        images.append(image)
    
    # Add final structure
    images.append(final_atoms.copy())
    
    print(f"Created {len(images)} total images (including initial and final)")
    return images


def calculate_tangent(prev_image: Atoms, current_image: Atoms, next_image: Atoms) -> np.ndarray:
    """
    Calculate the tangent vector for the current image using the improved tangent method.
    
    Args:
        prev_image: Previous image in the path
        current_image: Current image
        next_image: Next image in the path
    
    Returns:
        Normalized tangent vector (flattened positions)
    """
    # Get positions as flattened arrays
    prev_pos = prev_image.get_positions().flatten()
    curr_pos = current_image.get_positions().flatten()
    next_pos = next_image.get_positions().flatten()
    
    # Calculate vectors to neighboring images
    vec_to_next = next_pos - curr_pos
    vec_to_prev = curr_pos - prev_pos
    
    # Use the bisector method for tangent estimation
    tangent = vec_to_next + vec_to_prev
    
    # Normalize tangent
    tangent_norm = np.linalg.norm(tangent)
    if tangent_norm < 1e-10:
        # If tangent is nearly zero, use simple difference
        tangent = vec_to_next - vec_to_prev
        tangent_norm = np.linalg.norm(tangent)
        if tangent_norm < 1e-10:
            # If still zero, return zero tangent
            return np.zeros_like(tangent)
    
    return tangent / tangent_norm


def calculate_spring_force(prev_image: Atoms, current_image: Atoms, next_image: Atoms, 
                          tangent: np.ndarray, spring_constant: float) -> np.ndarray:
    """
    Calculate the spring force for the current image.
    
    Args:
        prev_image: Previous image in the path
        current_image: Current image
        next_image: Next image in the path
        tangent: Normalized tangent vector
        spring_constant: Spring constant (eV/Å²)
    
    Returns:
        Spring force vector (flattened)
    """
    # Get positions as flattened arrays
    prev_pos = prev_image.get_positions().flatten()
    curr_pos = current_image.get_positions().flatten()
    next_pos = next_image.get_positions().flatten()
    
    # Calculate distances to neighboring images
    dist_to_next = np.linalg.norm(next_pos - curr_pos)
    dist_to_prev = np.linalg.norm(curr_pos - prev_pos)
    
    # Spring force magnitude (difference in distances)
    spring_force_magnitude = spring_constant * (dist_to_next - dist_to_prev)
    
    # Apply force along tangent direction
    spring_force = spring_force_magnitude * tangent
    
    return spring_force


def calculate_neb_forces(images: List[Atoms], calculator, spring_constant: float, 
                        climbing_image_idx: Optional[int] = None) -> Tuple[List[np.ndarray], List[float]]:
    """
    Calculate NEB forces for all intermediate images.
    
    Args:
        images: List of all images in the path
        calculator: ASE calculator for force calculations
        spring_constant: Spring constant for NEB
        climbing_image_idx: Index of climbing image for CI-NEB (None for standard NEB)
    
    Returns:
        Tuple of (neb_forces_list, energies_list)
    """
    print("Calculating NEB forces...")
    
    neb_forces = []
    energies = []
    
    # Calculate energies and true forces for all images
    true_forces_list = []
    for i, image in enumerate(images):
        image.set_calculator(calculator)
        try:
            energy = image.get_potential_energy()
            true_force = image.get_forces()
            energies.append(energy)
            true_forces_list.append(true_force.flatten())
            print(f"  Image {i}: Energy = {energy:.6f} eV")
        except Exception as e:
            print(f"  Error calculating energy/forces for image {i}: {e}")
            raise
    
    # Calculate NEB forces for intermediate images
    for i in range(1, len(images) - 1):  # Skip first and last (fixed endpoints)
        # Calculate tangent
        tangent = calculate_tangent(images[i-1], images[i], images[i+1])
        
        # Get true force
        true_force = true_forces_list[i]
        
        if climbing_image_idx is not None and i == climbing_image_idx:
            # CI-NEB force calculation for climbing image
            parallel_component = np.dot(true_force, tangent) * tangent
            neb_force = true_force - 2 * parallel_component
            print(f"  Image {i} (CLIMBING): Applied CI-NEB force modification")
        else:
            # Standard NEB force calculation
            # Project true force perpendicular to tangent
            parallel_component = np.dot(true_force, tangent) * tangent
            perpendicular_force = true_force - parallel_component
            
            # Calculate spring force
            spring_force = calculate_spring_force(images[i-1], images[i], images[i+1], 
                                                tangent, spring_constant)
            
            # Combine perpendicular true force with parallel spring force
            parallel_spring_component = np.dot(spring_force, tangent) * tangent
            neb_force = perpendicular_force + parallel_spring_component
        
        neb_forces.append(neb_force)
    
    return neb_forces, energies


def save_neb_images(images: List[Atoms], output_dir: str, prefix: str, iteration: int = 0):
    """
    Save all NEB images to files.
    
    Args:
        images: List of ASE Atoms objects
        output_dir: Output directory
        prefix: Filename prefix
        iteration: Current iteration number
    """
    images_dir = os.path.join(output_dir, f"images_iter_{iteration:04d}")
    os.makedirs(images_dir, exist_ok=True)
    
    for i, image in enumerate(images):
        # Save as CIF
        cif_filename = f"{prefix}_image_{i:02d}.cif"
        cif_path = os.path.join(images_dir, cif_filename)
        write(cif_path, image)
        
        # Save as XYZ for visualization
        xyz_filename = f"{prefix}_image_{i:02d}.xyz"
        xyz_path = os.path.join(images_dir, xyz_filename)
        write(xyz_path, image)
    
    print(f"Saved {len(images)} images to {images_dir}")


def check_neb_convergence(neb_forces: List[np.ndarray], force_tolerance: float) -> Tuple[bool, float]:
    """
    Check if NEB calculation has converged.

    Args:
        neb_forces: List of NEB force vectors
        force_tolerance: Force tolerance for convergence

    Returns:
        Tuple of (converged, max_force)
    """
    if not neb_forces:
        return False, float('inf')

    # Calculate maximum force magnitude across all intermediate images
    max_force = 0.0
    for force in neb_forces:
        force_magnitude = np.linalg.norm(force)
        max_force = max(max_force, force_magnitude)

    converged = max_force < force_tolerance
    return converged, max_force


def update_image_positions(images: List[Atoms], neb_forces: List[np.ndarray], step_size: float = 0.01):
    """
    Update positions of intermediate images using steepest descent.

    Args:
        images: List of all images (endpoints remain fixed)
        neb_forces: List of NEB forces for intermediate images
        step_size: Step size for position updates
    """
    # Update only intermediate images (skip first and last)
    for i, force in enumerate(neb_forces):
        image_idx = i + 1  # Offset by 1 since we skip the first image

        # Reshape force back to (N_atoms, 3) format
        force_reshaped = force.reshape(-1, 3)

        # Update positions using steepest descent
        current_positions = images[image_idx].get_positions()
        new_positions = current_positions + step_size * force_reshaped
        images[image_idx].set_positions(new_positions)


def find_highest_energy_image(energies: List[float]) -> int:
    """
    Find the index of the highest energy intermediate image.

    Args:
        energies: List of energies for all images

    Returns:
        Index of highest energy intermediate image (excluding endpoints)
    """
    # Only consider intermediate images (exclude first and last)
    intermediate_energies = energies[1:-1]
    if not intermediate_energies:
        return None

    # Find index of maximum energy among intermediate images
    max_idx = np.argmax(intermediate_energies)

    # Convert back to full image list index
    return max_idx + 1


def run_neb_optimization(initial_atoms: Atoms, final_atoms: Atoms, calculator,
                        num_images: int, spring_constant: float, max_iterations: int,
                        force_tolerance: float, output_dir: str, prefix: str,
                        climbing_start_iteration: Optional[int] = None) -> Dict[str, Any]:
    """
    Run NEB or CI-NEB optimization.

    Args:
        initial_atoms: Initial structure
        final_atoms: Final structure
        calculator: ASE calculator
        num_images: Number of intermediate images
        spring_constant: Spring constant for NEB
        max_iterations: Maximum number of iterations
        force_tolerance: Force tolerance for convergence
        output_dir: Output directory
        prefix: Filename prefix
        climbing_start_iteration: Iteration to start climbing image (None for standard NEB)

    Returns:
        Dictionary with optimization results
    """
    print(f"\n--- Starting {'CI-NEB' if climbing_start_iteration else 'NEB'} Optimization ---")
    print(f"Number of intermediate images: {num_images}")
    print(f"Spring constant: {spring_constant} eV/Å²")
    print(f"Force tolerance: {force_tolerance} eV/Å")
    print(f"Maximum iterations: {max_iterations}")
    if climbing_start_iteration:
        print(f"Climbing image starts at iteration: {climbing_start_iteration}")

    start_time = time.time()

    # Create initial path by linear interpolation
    images = linear_interpolate_structures(initial_atoms, final_atoms, num_images)

    # Save initial path
    save_neb_images(images, output_dir, prefix, iteration=0)

    # Optimization loop
    climbing_image_idx = None
    convergence_history = []

    for iteration in range(1, max_iterations + 1):
        print(f"\n--- NEB Iteration {iteration} ---")

        # Determine if we should activate climbing image
        if (climbing_start_iteration is not None and
            iteration >= climbing_start_iteration and
            climbing_image_idx is None):
            # Calculate energies to find highest energy image
            temp_forces, energies = calculate_neb_forces(images, calculator, spring_constant)
            climbing_image_idx = find_highest_energy_image(energies)
            if climbing_image_idx is not None:
                print(f"Activating climbing image at index {climbing_image_idx}")

        # Calculate NEB forces
        neb_forces, energies = calculate_neb_forces(images, calculator, spring_constant,
                                                   climbing_image_idx)

        # Check convergence
        converged, max_force = check_neb_convergence(neb_forces, force_tolerance)
        convergence_history.append({'iteration': iteration, 'max_force': max_force, 'energies': energies.copy()})

        print(f"Max force: {max_force:.6f} eV/Å (tolerance: {force_tolerance:.6f})")

        if converged:
            print(f"NEB converged after {iteration} iterations!")
            break

        # Update positions
        update_image_positions(images, neb_forces)

        # Save images every 10 iterations or at the end
        if iteration % 10 == 0 or iteration == max_iterations:
            save_neb_images(images, output_dir, prefix, iteration)

    else:
        print(f"NEB did not converge after {max_iterations} iterations")

    # Save final path
    save_neb_images(images, output_dir, prefix, iteration=max_iterations)

    end_time = time.time()
    optimization_time = end_time - start_time

    print(f"NEB optimization completed in {optimization_time:.2f} seconds")

    # Return results
    results = {
        'converged': converged,
        'final_max_force': max_force,
        'iterations': iteration,
        'optimization_time': optimization_time,
        'images': images,
        'convergence_history': convergence_history,
        'climbing_image_idx': climbing_image_idx,
        'final_energies': energies
    }

    return results
